import pytest
from datetime import datetime, timedelta, timezone
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.settings import get_settings
from app.models.inventory import Product, ProductCategory
from app.models.sales import Order, OrderItem, OrderStatus, PaymentMethod
from app.models.user import User, UserRole

settings = get_settings()


async def get_auth_headers(client: AsyncClient, user: User) -> dict:
    """Helper function to get authentication headers."""
    login_data = {
        "username": user.email,
        "password": "password123",
    }
    response = await client.post(
        f"{settings.API_V1_STR}/auth/login",
        data=login_data,
    )
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
async def test_product(db: AsyncSession) -> Product:
    """Create a test product."""
    product_data = {
        "name": "Test Product",
        "description": "A test product for sales",
        "sku": "SALES001",
        "category": ProductCategory.GROCERY,
        "price": 10.99,
        "cost": 5.50,
        "quantity": 100,
        "reorder_level": 10,
    }
    product = Product(**product_data)
    db.add(product)
    await db.commit()
    await db.refresh(product)
    return product


@pytest.fixture
async def test_order(db: AsyncSession, test_user: User, test_product: Product) -> Order:
    """Create a test order."""
    order = Order(
        customer_name="Test Customer",
        total_amount=21.98,
        payment_method=PaymentMethod.CASH,
        status=OrderStatus.PENDING,
        cashier_id=test_user.id,
    )
    db.add(order)
    await db.commit()
    await db.refresh(order)
    
    # Add order items
    order_item = OrderItem(
        order_id=order.id,
        product_id=test_product.id,
        quantity=2,
        unit_price=test_product.price,
    )
    db.add(order_item)
    await db.commit()
    
    return order


@pytest.fixture
async def test_manager(db: AsyncSession) -> User:
    """Create a test manager user."""
    manager_data = {
        "email": "<EMAIL>",
        "password": "password123",
        "full_name": "Sales Manager",
        "role": UserRole.MANAGER,
    }
    manager = await User.create(db, obj_in=manager_data)
    return manager


# Order Tests
@pytest.mark.asyncio
async def test_create_order_success(
    client: AsyncClient,
    test_user: User,
    test_product: Product,
):
    """Test creating a new order."""
    headers = await get_auth_headers(client, test_user)
    
    order_data = {
        "customer_name": "John Doe",
        "payment_method": "cash",
        "items": [
            {
                "product_id": test_product.id,
                "quantity": 2,
                "unit_price": test_product.price,
            }
        ],
    }
    
    response = await client.post(
        f"{settings.API_V1_STR}/sales/orders",
        json=order_data,
        headers=headers,
    )
    assert response.status_code == 200
    data = response.json()
    assert data["customer_name"] == order_data["customer_name"]
    assert data["payment_method"] == order_data["payment_method"]
    assert data["status"] == "pending"
    assert "id" in data
    assert len(data["items"]) == 1


@pytest.mark.asyncio
async def test_create_order_insufficient_stock(
    client: AsyncClient,
    test_user: User,
    test_product: Product,
):
    """Test creating order with insufficient stock."""
    headers = await get_auth_headers(client, test_user)
    
    order_data = {
        "customer_name": "John Doe",
        "payment_method": "cash",
        "items": [
            {
                "product_id": test_product.id,
                "quantity": test_product.quantity + 50,  # More than available
                "unit_price": test_product.price,
            }
        ],
    }
    
    response = await client.post(
        f"{settings.API_V1_STR}/sales/orders",
        json=order_data,
        headers=headers,
    )
    assert response.status_code == 400
    assert "insufficient stock" in response.json()["detail"].lower()


@pytest.mark.asyncio
async def test_create_order_nonexistent_product(
    client: AsyncClient,
    test_user: User,
):
    """Test creating order with non-existent product."""
    headers = await get_auth_headers(client, test_user)
    
    order_data = {
        "customer_name": "John Doe",
        "payment_method": "cash",
        "items": [
            {
                "product_id": 99999,  # Non-existent product
                "quantity": 1,
                "unit_price": 10.99,
            }
        ],
    }
    
    response = await client.post(
        f"{settings.API_V1_STR}/sales/orders",
        json=order_data,
        headers=headers,
    )
    assert response.status_code == 404


@pytest.mark.asyncio
async def test_get_orders_success(
    client: AsyncClient,
    test_user: User,
    test_order: Order,
):
    """Test getting orders list."""
    headers = await get_auth_headers(client, test_user)
    
    response = await client.get(
        f"{settings.API_V1_STR}/sales/orders",
        headers=headers,
    )
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) >= 1
    
    order = data[0]
    assert order["customer_name"] == test_order.customer_name
    assert order["status"] == test_order.status.value


@pytest.mark.asyncio
async def test_get_orders_pagination(
    client: AsyncClient,
    test_user: User,
    test_order: Order,
):
    """Test orders pagination."""
    headers = await get_auth_headers(client, test_user)
    
    # Test with limit
    response = await client.get(
        f"{settings.API_V1_STR}/sales/orders?limit=1",
        headers=headers,
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data) <= 1
    
    # Test with skip
    response = await client.get(
        f"{settings.API_V1_STR}/sales/orders?skip=0&limit=10",
        headers=headers,
    )
    assert response.status_code == 200


@pytest.mark.asyncio
async def test_get_order_by_id_success(
    client: AsyncClient,
    test_user: User,
    test_order: Order,
):
    """Test getting a specific order by ID."""
    headers = await get_auth_headers(client, test_user)
    
    response = await client.get(
        f"{settings.API_V1_STR}/sales/orders/{test_order.id}",
        headers=headers,
    )
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_order.id
    assert data["customer_name"] == test_order.customer_name
    assert "items" in data


@pytest.mark.asyncio
async def test_get_order_by_id_not_found(
    client: AsyncClient,
    test_user: User,
):
    """Test getting non-existent order."""
    headers = await get_auth_headers(client, test_user)
    
    response = await client.get(
        f"{settings.API_V1_STR}/sales/orders/99999",
        headers=headers,
    )
    assert response.status_code == 404


@pytest.mark.asyncio
async def test_update_order_success(
    client: AsyncClient,
    test_user: User,
    test_order: Order,
):
    """Test updating an order."""
    headers = await get_auth_headers(client, test_user)
    
    update_data = {
        "customer_name": "Updated Customer Name",
        "payment_method": "card",
    }
    
    response = await client.put(
        f"{settings.API_V1_STR}/sales/orders/{test_order.id}",
        json=update_data,
        headers=headers,
    )
    assert response.status_code == 200
    data = response.json()
    assert data["customer_name"] == update_data["customer_name"]
    assert data["payment_method"] == update_data["payment_method"]


@pytest.mark.asyncio
async def test_complete_order_success(
    client: AsyncClient,
    test_user: User,
    test_order: Order,
    test_product: Product,
    db: AsyncSession,
):
    """Test completing an order."""
    headers = await get_auth_headers(client, test_user)
    
    # Get initial product quantity
    await db.refresh(test_product)
    initial_quantity = test_product.quantity
    
    response = await client.post(
        f"{settings.API_V1_STR}/sales/orders/{test_order.id}/complete",
        headers=headers,
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "completed"
    
    # Verify inventory was updated
    await db.refresh(test_product)
    assert test_product.quantity < initial_quantity


@pytest.mark.asyncio
async def test_complete_already_completed_order(
    client: AsyncClient,
    test_user: User,
    test_order: Order,
    db: AsyncSession,
):
    """Test completing an already completed order."""
    headers = await get_auth_headers(client, test_user)
    
    # First complete the order
    test_order.status = OrderStatus.COMPLETED
    db.add(test_order)
    await db.commit()
    
    response = await client.post(
        f"{settings.API_V1_STR}/sales/orders/{test_order.id}/complete",
        headers=headers,
    )
    assert response.status_code == 400


@pytest.mark.asyncio
async def test_cancel_order_success(
    client: AsyncClient,
    test_user: User,
    test_order: Order,
):
    """Test cancelling an order."""
    headers = await get_auth_headers(client, test_user)
    
    response = await client.post(
        f"{settings.API_V1_STR}/sales/orders/{test_order.id}/cancel",
        headers=headers,
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "cancelled"


@pytest.mark.asyncio
async def test_refund_order_success(
    client: AsyncClient,
    test_manager: User,
    test_order: Order,
    test_product: Product,
    db: AsyncSession,
):
    """Test refunding a completed order."""
    headers = await get_auth_headers(client, test_manager)
    
    # First complete the order
    test_order.status = OrderStatus.COMPLETED
    db.add(test_order)
    await db.commit()
    
    # Get initial product quantity
    await db.refresh(test_product)
    initial_quantity = test_product.quantity
    
    response = await client.post(
        f"{settings.API_V1_STR}/sales/orders/{test_order.id}/refund",
        headers=headers,
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "refunded"
    
    # Verify inventory was restored
    await db.refresh(test_product)
    assert test_product.quantity > initial_quantity


@pytest.mark.asyncio
async def test_refund_order_insufficient_permissions(
    client: AsyncClient,
    test_user: User,  # Cashier role
    test_order: Order,
    db: AsyncSession,
):
    """Test refunding order with insufficient permissions."""
    headers = await get_auth_headers(client, test_user)
    
    # First complete the order
    test_order.status = OrderStatus.COMPLETED
    db.add(test_order)
    await db.commit()
    
    response = await client.post(
        f"{settings.API_V1_STR}/sales/orders/{test_order.id}/refund",
        headers=headers,
    )
    assert response.status_code == 403


# Sales Reports and Analytics Tests
@pytest.mark.asyncio
async def test_get_sales_summary_success(
    client: AsyncClient,
    test_manager: User,
    test_order: Order,
    db: AsyncSession,
):
    """Test getting sales summary."""
    headers = await get_auth_headers(client, test_manager)

    # Complete the order to include it in sales
    test_order.status = OrderStatus.COMPLETED
    db.add(test_order)
    await db.commit()

    # Get today's date range
    today = datetime.now(timezone.utc).date()
    date_range = {
        "start_date": today.isoformat(),
        "end_date": today.isoformat(),
    }

    response = await client.post(
        f"{settings.API_V1_STR}/sales/summary",
        json=date_range,
        headers=headers,
    )
    assert response.status_code == 200
    data = response.json()
    assert "total_sales" in data
    assert "total_orders" in data
    assert "average_order_value" in data
    assert data["total_orders"] >= 1


@pytest.mark.asyncio
async def test_get_sales_summary_date_range(
    client: AsyncClient,
    test_manager: User,
):
    """Test sales summary with custom date range."""
    headers = await get_auth_headers(client, test_manager)

    # Test with last 7 days
    end_date = datetime.now(timezone.utc).date()
    start_date = end_date - timedelta(days=7)

    date_range = {
        "start_date": start_date.isoformat(),
        "end_date": end_date.isoformat(),
    }

    response = await client.post(
        f"{settings.API_V1_STR}/sales/summary",
        json=date_range,
        headers=headers,
    )
    assert response.status_code == 200
    data = response.json()
    assert "total_sales" in data
    assert "total_orders" in data


@pytest.mark.asyncio
async def test_get_sales_summary_invalid_date_range(
    client: AsyncClient,
    test_manager: User,
):
    """Test sales summary with invalid date range."""
    headers = await get_auth_headers(client, test_manager)

    # End date before start date
    date_range = {
        "start_date": "2023-12-31",
        "end_date": "2023-01-01",
    }

    response = await client.post(
        f"{settings.API_V1_STR}/sales/summary",
        json=date_range,
        headers=headers,
    )
    assert response.status_code == 400


@pytest.mark.asyncio
async def test_get_sales_summary_insufficient_permissions(
    client: AsyncClient,
    test_user: User,  # Cashier role
):
    """Test sales summary with insufficient permissions."""
    headers = await get_auth_headers(client, test_user)

    date_range = {
        "start_date": "2023-01-01",
        "end_date": "2023-12-31",
    }

    response = await client.post(
        f"{settings.API_V1_STR}/sales/summary",
        json=date_range,
        headers=headers,
    )
    assert response.status_code == 403


# Edge Cases and Error Handling
@pytest.mark.asyncio
async def test_create_order_empty_items(
    client: AsyncClient,
    test_user: User,
):
    """Test creating order with empty items list."""
    headers = await get_auth_headers(client, test_user)

    order_data = {
        "customer_name": "John Doe",
        "payment_method": "cash",
        "items": [],  # Empty items
    }

    response = await client.post(
        f"{settings.API_V1_STR}/sales/orders",
        json=order_data,
        headers=headers,
    )
    assert response.status_code == 422


@pytest.mark.asyncio
async def test_create_order_invalid_payment_method(
    client: AsyncClient,
    test_user: User,
    test_product: Product,
):
    """Test creating order with invalid payment method."""
    headers = await get_auth_headers(client, test_user)

    order_data = {
        "customer_name": "John Doe",
        "payment_method": "invalid_method",
        "items": [
            {
                "product_id": test_product.id,
                "quantity": 1,
                "unit_price": test_product.price,
            }
        ],
    }

    response = await client.post(
        f"{settings.API_V1_STR}/sales/orders",
        json=order_data,
        headers=headers,
    )
    assert response.status_code == 422


@pytest.mark.asyncio
async def test_create_order_negative_quantity(
    client: AsyncClient,
    test_user: User,
    test_product: Product,
):
    """Test creating order with negative quantity."""
    headers = await get_auth_headers(client, test_user)

    order_data = {
        "customer_name": "John Doe",
        "payment_method": "cash",
        "items": [
            {
                "product_id": test_product.id,
                "quantity": -1,  # Negative quantity
                "unit_price": test_product.price,
            }
        ],
    }

    response = await client.post(
        f"{settings.API_V1_STR}/sales/orders",
        json=order_data,
        headers=headers,
    )
    assert response.status_code == 422


@pytest.mark.asyncio
async def test_order_lifecycle_complete_flow(
    client: AsyncClient,
    test_user: User,
    test_product: Product,
    db: AsyncSession,
):
    """Test complete order lifecycle from creation to completion."""
    headers = await get_auth_headers(client, test_user)

    # 1. Create order
    order_data = {
        "customer_name": "Lifecycle Test Customer",
        "payment_method": "cash",
        "items": [
            {
                "product_id": test_product.id,
                "quantity": 1,
                "unit_price": test_product.price,
            }
        ],
    }

    response = await client.post(
        f"{settings.API_V1_STR}/sales/orders",
        json=order_data,
        headers=headers,
    )
    assert response.status_code == 200
    order = response.json()
    order_id = order["id"]

    # 2. Verify order is pending
    assert order["status"] == "pending"

    # 3. Complete order
    response = await client.post(
        f"{settings.API_V1_STR}/sales/orders/{order_id}/complete",
        headers=headers,
    )
    assert response.status_code == 200
    completed_order = response.json()
    assert completed_order["status"] == "completed"

    # 4. Verify order cannot be completed again
    response = await client.post(
        f"{settings.API_V1_STR}/sales/orders/{order_id}/complete",
        headers=headers,
    )
    assert response.status_code == 400
