import pytest
from fastapi import <PERSON><PERSON><PERSON>
from httpx import <PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.settings import get_settings
from app.models.user import User

settings = get_settings()


@pytest.mark.asyncio
async def test_login(
    client: AsyncClient,
    test_user: User,
):
    """Test user login."""
    # Test successful login
    login_data = {
        "username": test_user.email,
        "password": "password123",
    }
    response = await client.post(
        f"{settings.API_V1_STR}/auth/login",
        data=login_data,
    )
    assert response.status_code == 200
    token = response.json()
    assert "access_token" in token
    assert token["token_type"] == "bearer"
    
    # Test invalid credentials
    invalid_login = {
        "username": test_user.email,
        "password": "wrongpassword",
    }
    response = await client.post(
        f"{settings.API_V1_STR}/auth/login",
        data=invalid_login,
    )
    assert response.status_code == 401


@pytest.mark.asyncio
async def test_register(
    client: AsyncClient,
    db: AsyncSession,
):
    """Test user registration."""
    # Test successful registration
    user_data = {
        "email": "<EMAIL>",
        "password": "newpassword123",
        "full_name": "New User",
    }
    response = await client.post(
        f"{settings.API_V1_STR}/auth/register",
        json=user_data,
    )
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == user_data["email"]
    assert data["full_name"] == user_data["full_name"]
    
    # Test duplicate email
    response = await client.post(
        f"{settings.API_V1_STR}/auth/register",
        json=user_data,
    )
    assert response.status_code == 400


@pytest.mark.asyncio
async def test_get_me(
    client: AsyncClient,
    test_user: User,
):
    """Test get current user endpoint."""
    # Login first
    login_data = {
        "username": test_user.email,
        "password": "password123",
    }
    response = await client.post(
        f"{settings.API_V1_STR}/auth/login",
        data=login_data,
    )
    token = response.json()["access_token"]
    
    # Test get me endpoint
    response = await client.get(
        f"{settings.API_V1_STR}/auth/me",
        headers={"Authorization": f"Bearer {token}"},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == test_user.email
    assert data["full_name"] == test_user.full_name
    
    # Test without token
    response = await client.get(f"{settings.API_V1_STR}/auth/me")
    assert response.status_code == 401
