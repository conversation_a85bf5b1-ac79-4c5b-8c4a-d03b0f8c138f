#!/usr/bin/env python
import os
from dotenv import load_dotenv

# Load .env file
load_dotenv()

print("Environment variables from .env:")
print(f"ENV_MODE: '{os.getenv('ENV_MODE')}'")
print(f"ACCESS_TOKEN_EXPIRE_MINUTES: '{os.getenv('ACCESS_TOKEN_EXPIRE_MINUTES')}'")
print(f"LOG_LEVEL: '{os.getenv('LOG_LEVEL')}'")
print(f"SQLITE_DATABASE_URI: '{os.getenv('SQLITE_DATABASE_URI')}'")

# Try importing settings
try:
    from app.core.settings import get_settings
    settings = get_settings()
    print("\nSettings loaded successfully!")
    print(f"ENV_MODE: {settings.ENV_MODE}")
    print(f"ACCESS_TOKEN_EXPIRE_MINUTES: {settings.ACCESS_TOKEN_EXPIRE_MINUTES}")
    print(f"LOG_LEVEL: {settings.LOG_LEVEL}")
    print(f"SQLITE_DATABASE_URI: {settings.SQLITE_DATABASE_URI}")
except Exception as e:
    print(f"\nError loading settings: {e}")
